<template>
  <div class="Bt1">
    <div id="pieChart" class="pie-chart"></div>
    <div class="Bt1Img1"></div>
    <div class="Bt1Img2"></div>
    <div class="Bt1Img3"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, onUnmounted, computed, watch, ref } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

// 定义props
const props = defineProps({
  enableLoadingAnimation: {
    type: Boolean,
    default: false,
  },
  animationDelay: {
    type: String,
    default: "0s",
  },
});

let myChart = null;
const isChartReady = ref(false);

const animationConfig = {
  // ==================== 全局动画配置 ====================
  speedMultiplier: 1, // 整体动画速度倍数 (0.5=慢一半, 1.0=正常, 2.0=快一倍)

  // ==================== 主动画配置 ====================
  baseDuration: 1200, // 基础动画持续时间(ms) - 会被speedMultiplier调整
  easing: "cubicOut", // 缓动函数 - quartOut提供平滑的减速效果
  baseDelayInterval: 60, // 基础元素间延迟间隔(ms) - 会被speedMultiplier调整
  type: "expansion", // 动画类型 - expansion从中心向外扩展
  threshold: 2000, // 动画阈值 - 数据量超过此值时禁用动画以提升性能

  // ==================== 标签动画配置 ====================
  baseLabelDuration: 800, // 基础标签动画持续时间(ms) - 会被speedMultiplier调整
  labelEasing: "cubicOut", // 标签缓动函数 - 与主动画保持一致

  // ==================== 标签线动画配置 ====================
  baseLabelLineDuration: 600, // 基础标签线动画持续时间(ms) - 会被speedMultiplier调整
  labelLineEasing: "cubicOut", // 标签线缓动函数 - 与主动画保持一致

  // ==================== 加载动画配置 ====================
  baseLoadingDelay: 300, // 基础加载完成后的额外延迟(ms) - 会被speedMultiplier调整
  loadingText: "数据加载中...", // 加载提示文本
  loadingColor: "#32FEFC", // 加载动画颜色 - 与主题色保持一致
  loadingTextColor: "#ffffff", // 加载文本颜色
  loadingMaskColor: "rgba(0, 0, 0, 0.3)", // 加载遮罩颜色 - 半透明黑色
  loadingFontSize: 12, // 加载文本字体大小
  loadingSpinnerRadius: 10, // 加载旋转器半径
  loadingLineWidth: 2, // 加载线条宽度
};

// 计算属性：根据速度倍数计算实际的动画参数
const computedAnimationConfig = computed(() => {
  const speed = animationConfig.speedMultiplier;
  return {
    duration: Math.round(animationConfig.baseDuration / speed),
    delayInterval: Math.round(animationConfig.baseDelayInterval / speed),
    labelDuration: Math.round(animationConfig.baseLabelDuration / speed),
    labelLineDuration: Math.round(animationConfig.baseLabelLineDuration / speed),
    loadingDelay: Math.round(animationConfig.baseLoadingDelay / speed),
  };
});

// 计算属性：从 dataStore 获取动态数据
const chartData = computed(() => {
  const alarmRisk = dataStore.data?.alarmRisk;
  if (!alarmRisk) {
    // 如果数据还未加载，返回默认值
    return [
      {
        value: 0,
        name: "高危攻击",
        itemStyle: { color: "#FD9391" },
        labelLine: { lineStyle: { color: "#FD9391" } },
      },
      {
        value: 0,
        name: "低危攻击",
        itemStyle: { color: "#32FEFC" },
        labelLine: { lineStyle: { color: "#32FEFC" } },
      },
      {
        value: 0,
        name: "中危攻击",
        itemStyle: { color: "#95C1FA" },
        labelLine: { lineStyle: { color: "#95C1FA" } },
      },
    ];
  }

  return [
    {
      value: alarmRisk.highNum || 0,
      name: "高危攻击",
      itemStyle: { color: "#FD9391" },
      labelLine: { lineStyle: { color: "#FD9391" } },
    },
    {
      value: alarmRisk.lowNum || 0,
      name: "低危攻击",
      itemStyle: { color: "#32FEFC" },
      labelLine: { lineStyle: { color: "#32FEFC" } },
    },
    {
      value: alarmRisk.middleNum || 0,
      name: "中危攻击",
      itemStyle: { color: "#95C1FA" },
      labelLine: { lineStyle: { color: "#95C1FA" } },
    },
  ];
});

const initChart = () => {
  const chartDom = document.getElementById("pieChart");
  if (chartDom) {
    myChart = echarts.init(chartDom);

    if (props.enableLoadingAnimation) {
      // 如果启用加载动画，先显示加载状态
      myChart.showLoading({
        text: animationConfig.loadingText,
        color: animationConfig.loadingColor,
        textColor: animationConfig.loadingTextColor,
        maskColor: animationConfig.loadingMaskColor,
        zlevel: 0,
        fontSize: animationConfig.loadingFontSize,
        showSpinner: true,
        spinnerRadius: animationConfig.loadingSpinnerRadius,
        lineWidth: animationConfig.loadingLineWidth,
      });

      // 根据animationDelay延迟显示图表
      const delay = parseFloat(props.animationDelay) * 1000 || 0;
      setTimeout(() => {
        myChart.hideLoading();
        updateChart();
        isChartReady.value = true;
      }, delay + computedAnimationConfig.value.loadingDelay);
    } else {
      updateChart();
      isChartReady.value = true;
    }

    // 监听窗口大小变化
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    // 添加动画配置
    animation: props.enableLoadingAnimation,
    animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.duration : 0,
    animationEasing: animationConfig.easing,
    animationDelay: function (idx) {
      return idx * computedAnimationConfig.value.delayInterval;
    },
    series: [
      {
        name: "数据统计",
        type: "pie",
        radius: ["30%", "36%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 0,
        },
        // 添加系列级别的动画配置
        animation: props.enableLoadingAnimation,
        animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.duration : 0,
        animationEasing: animationConfig.easing,
        animationThreshold: animationConfig.threshold,
        // 添加动画类型配置，使用expansion动画更平滑
        animationType: animationConfig.type,
        label: {
          show: true,
          position: "outside",
          fontSize: 12,
          color: "#ffffff",
          formatter: function (params) {
            return `{name|${params.name}}\n{value|${params.value}}`;
          },
          fontWeight: "normal",
          // 添加标签动画配置
          animation: props.enableLoadingAnimation,
          animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.labelDuration : 0,
          animationEasing: animationConfig.labelEasing,
          rich: {
            name: {
              fontSize: 12,
              color: "#a1a1a1",
              fontWeight: "normal",
              lineHeight: 16,
              align: "center",
            },
            value: {
              fontSize: 20,
              color: "#ffffff",
              fontWeight: "bold",
              lineHeight: 20,
              align: "center",
              padding: [18, 0, 0, 0],
            },
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold",
            formatter: function (params) {
              return `{name|${params.name}}\n{value|${params.value}}`;
            },
            rich: {
              name: {
                fontSize: 14,
                color: "#ffffff",
                fontWeight: "bold",
                lineHeight: 18,
                align: "center",
              },
              value: {
                fontSize: 16,
                color: "#32FEFC",
                fontWeight: "bold",
                lineHeight: 22,
                align: "center",
                padding: [8, 0, 0, 0],
              },
            },
          },
        },
        labelLine: {
          show: true,
          length: 20,
          length2: 20,
          lineStyle: {
            width: 1,
            // 标签线颜色将通过数据项的 labelLine 配置单独设置
          },
          // 添加标签线动画配置
          animation: props.enableLoadingAnimation,
          animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.labelLineDuration : 0,
          animationEasing: animationConfig.labelLineEasing,
        },
        data: chartData.value,
      },
    ],
  };

  myChart.setOption(option);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  () => {
    // 只有在图表准备好后才更新
    if (isChartReady.value) {
      updateChart();
    }
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Bt1 {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  pointer-events: all;
}

.pie-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
.Bt1Img1 {
  position: absolute;
  width: 58%;
  left: 97px;
  height: 118%;
  background-image: url("../../public/img/w.png");
  background-repeat: no-repeat;
  background-size: contain;
  pointer-events: none;
}
.Bt1Img2 {
  position: absolute;
  width: 49%;
  height: 117%;
  background-image: url("../../public/img/w2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  animation: rotate-medium 15s linear infinite;
  pointer-events: none;
}
.Bt1Img3 {
  position: absolute;
  width: 43%;
  height: 107%;
  background-image: url("../../public/img/w3.png");
  background-repeat: no-repeat;
  background-size: contain;
  animation: rotate-fast 10s linear infinite;
  pointer-events: none;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-medium {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
</style>
