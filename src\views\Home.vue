<template>
  <div class="Home">
    <transition name="slide-down">
      <div v-if="uiAnimationTriggered" class="HomeHead" :style="{ animationDelay: '0.1s' }">
        <div class="HomeHeadText">网络攻击态势</div>
        <div class="xq">{{ currentWeekday }}</div>
        <div class="time">{{ currentTime }}</div>
      </div>
    </transition>

    <div class="HomeBody">
      <div class="HomeBodyTop">
        <transition name="slide-left">
          <div v-if="uiAnimationTriggered" class="HomeLeft" :style="{ animationDelay: '0.1s' }"><Left /></div>
        </transition>
        <transition name="slide-right">
          <div v-if="uiAnimationTriggered" class="HomeRight" :style="{ animationDelay: '0.1s' }"><Right /></div>
        </transition>
      </div>
      <transition name="slide-up">
        <div v-if="uiAnimationTriggered" class="HomeBodyBom" :style="{ animationDelay: '0.2s' }"></div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, inject } from "vue";
import Left from "./Left.vue";
import Right from "./Right.vue";

// 注入UI动画触发状态
const uiAnimationTriggered = inject("uiAnimationTriggered", ref(false));

// 响应式时间数据
const currentTime = ref("");
const currentWeekday = ref("");

// 星期映射
const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];

// 更新时间的函数
const updateTime = () => {
  const now = new Date();

  // 格式化时间为 YYYY-MM-DD HH:mm:ss
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

  // 获取星期
  const weekdayIndex = now.getDay();
  currentWeekday.value = weekdays[weekdayIndex];
};

// 定时器引用
let timeInterval = null;

// 组件挂载时启动定时器
onMounted(() => {
  // 立即更新一次时间
  updateTime();

  // 每秒更新一次时间
  timeInterval = setInterval(updateTime, 1000);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
    timeInterval = null;
  }
});
</script>

<style lang="less" scoped>
/* 从上向下滑动动画样式 */
.slide-down-enter-active {
  animation: slideInFromTop 0.8s ease-out forwards;
}

.slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 从下向上滑动动画样式 */
.slide-up-enter-active {
  animation: slideInFromBottom 0.8s ease-out forwards;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 从左向右滑动动画样式 */
.slide-left-enter-active {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 从右向左滑动动画样式 */
.slide-right-enter-active {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.slide-right-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 为每个元素添加初始状态和动画延迟 */
.HomeHead {
  animation: slideInFromTop 0.8s ease-out forwards;
  transform: translateY(-100%);
  opacity: 0;
}

.HomeLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
  transform: translateX(-100%);
  opacity: 0;
}

.HomeRight {
  animation: slideInFromRight 0.8s ease-out forwards;
  transform: translateX(100%);
  opacity: 0;
}

.HomeBodyBom {
  animation: slideInFromBottom 0.8s ease-out forwards;
  transform: translateY(100%);
  opacity: 0;
}

.Home {
  width: 100%;
  height: 100%;
  pointer-events: none;
  .HomeHead {
    width: calc(100% - 84px);
    height: 90px;
    background-image: url("../../public/img/head.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-top: 14px;
    margin-left: 42px;
    margin-right: 42px;

    .HomeHeadText {
      position: absolute;
      width: 100%;
      height: 100%;
      font-family: PangMenZhengDaoBiaoTiTiMianFeiBan, PangMenZhengDaoBiaoTiTiMianFeiBan;
      font-weight: normal;
      font-size: 36px;
      color: #ffffff;
      letter-spacing: 8px;
      font-style: normal;

      display: flex;
      justify-content: center;
      align-items: center;

      top: -20px;
    }
  }
  .xq {
    position: absolute;
    top: 0;
    left: 0;
    position: absolute;
    top: 55px;
    left: 200px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    line-height: 19px;
    text-align: left;
    font-style: normal;
  }

  .time {
    position: absolute;
    top: 55px;
    right: 90px;

    font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    line-height: 19px;
    text-align: left;
    font-style: normal;
  }
  .HomeBody {
    width: 100%;
    height: calc(100% - 90px);

    .HomeBodyTop {
      width: 100%;
      height: calc(100% - 88px);

      display: flex;
      justify-content: center;
      align-items: center;
      .HomeLeft {
        width: 50%;
        height: 100%;

        background-image: url("../../public/img/left.png");
        background-repeat: no-repeat;
        background-position: 1%;
        background-size: 150px 100%;

        .HomeLeft1 {
          width: 300px;
          height: 200px;
        }
      }
      .HomeRight {
        width: 50%;
        height: 100%;
        background-image: url("../../public/img/right.png");
        background-repeat: no-repeat;
        background-position: 99%;
        background-size: 150px 100%;
      }
    }

    .HomeBodyBom {
      position: relative;
      margin-top: -84px;
      width: 100%;
      height: 149px;
      background-image: url("../../public/img/bom.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
