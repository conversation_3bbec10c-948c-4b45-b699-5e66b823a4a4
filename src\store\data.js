import axios from "axios";
import { defineStore } from "pinia";
let num = 0;
let num1 = 0;
export const usedata = defineStore("data", {
  state: () => ({
    data: {},
    ws: null,
    wsConnected: false,
    colasoftData: {
      xAxisData: [],
      seriesData: [],
    },
    attackAlarmData: [], // 存储攻击告警数据
  }),
  getters: {},
  actions: {
    async getData() {
      const res = await axios.get("http://223.70.137.155:20080/huwang/activity/screentwo?eventId=15");
      this.data = res.data.data;

      setTimeout(() => {
        this.getData();
      }, 3000);
      // this.data.attackCountryTop.push({ country: "美国" + num++, attackNum: Math.floor(Math.random() * 1000) });
      // this.data.attackCountryTop.push({ country: "美国" + num++, attackNum: Math.floor(Math.random() * 1000) });
      // this.data.attackCountryTop.push({ country: "美国" + num++, attackNum: Math.floor(Math.random() * 1000) });
      // this.data.attackCountryTop.push({ country: "美国" + num++, attackNum: Math.floor(Math.random() * 1000) });
      // this.data.attackCountryTop.push({ country: "美国" + num++, attackNum: Math.floor(Math.random() * 1000) });

      window.addEventListener("keydown", (e) => {
        if (e.key == "e") {
          this.data.attackCountryTop.push({ country: "美国" + num++, attackNum: Math.random() * 1000 });
        }
      });
    },
    getWss() {
      try {
        // 如果已经有连接，先关闭
        if (this.ws) {
          this.ws.close();
        }

        // 创建 WebSocket 连接
        this.ws = new WebSocket("ws://223.70.137.155:20080/huwang/activity/screen-wss");

        // 连接打开时的处理
        this.ws.onopen = () => {
          console.log("🚀 WebSocket 连接已建立");
          this.wsConnected = true;
        };

        // 接收消息时的处理
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log("🚀 WebSocket 接收到数据:", data);
            // 处理 WebSocket 消息
            this.handleWebSocketMessage(data);
          } catch (error) {
            console.error("🚀 WebSocket 数据解析错误:", error);
          }
        };

        // 连接关闭时的处理
        this.ws.onclose = (event) => {
          console.log("🚀 WebSocket 连接已关闭:", event.code, event.reason);
          this.wsConnected = false;

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000) {
            console.log("🚀 WebSocket 尝试重连...");
            setTimeout(() => {
              this.getWss();
            }, 3000); // 3秒后重连
          }
        };

        // 连接错误时的处理
        this.ws.onerror = (error) => {
          console.error("🚀 WebSocket 连接错误:", error);
          this.wsConnected = false;
        };
      } catch (error) {
        console.error("🚀 WebSocket 创建失败:", error);
      }
      window.addEventListener("keydown", (e) => {
        if (e.key == "q") {
          this.csData();
        }
        if (e.key == "a") {
          this.addTestAttackData();
        }
      });
    },

    async getJqgj(id) {
      try {
        console.log("🚀 调用确认接口，ID:", id);
        const res = await axios.get(`http://223.70.137.155:20080/huwang/activity/screen-alarm-ignore?screenAlarmId=${id}`);
        console.log("🚀 确认接口响应:", res);
        return res;
      } catch (error) {
        console.error("🚀 确认接口调用失败:", error);
        throw error;
      }
    },

    csData() {
      num += 5;
      this.handleWebSocketMessage({
        msgType: "xdrNetworkLog",
        msgContent: [
          {
            startArray: {
              name: "美国",
              N: 38.9072 + num * 1.5, // 纬度变化幅度减小
              E: -77.0369 + num * 1.3, // 经度也要变化，避免飞线重叠
            },
            endArray: [
              {
                name: "北京",
                N: 39.904, // 北京纬度
                E: 116.407, // 北京经度（修正为正确值）
              },
            ],
            cardInfo: {
              hazardRating: "中危",
              alarmType: "HTTP_Protocol_Validation",
              attackTime: "2025-07-24 11:10:37",
              attackSip: "**************",
              attackDip: "**************",
              attackAddr: "美国",
              attackSport: 7223,
              attackDport: 443,
              ruleDescription: "",
              attackRes: "尝试",
              action: "允许",
              originProductType: "ZerolWorld(中瑞天下) 睿眼·Web攻击溯源系统",
              deviceIp: "*************",
            },
          },
        ],
      });
    },

    // 添加测试攻击数据
    addTestAttackData() {
      const testAttackTypes = [
        "SQL注入攻击",
        "XSS跨站脚本攻击",
        "CSRF跨站请求伪造",
        "文件上传漏洞",
        "命令注入攻击",
        "目录遍历攻击",
        "暴力破解攻击",
        "DDoS拒绝服务攻击",
        "恶意文件下载",
        "权限提升攻击",
      ];

      const testCountries = ["美国", "俄罗斯", "中国", "德国", "英国", "法国", "日本", "韩国", "印度", "巴西"];

      const testRiskLevels = ["低级", "中级", "高级", "严重"];
      const testStatuses = ["检测", "阻断", "警告", "成功", "失败"];

      // 生成随机测试数据
      const randomAttackType = testAttackTypes[Math.floor(Math.random() * testAttackTypes.length)];
      const randomCountry = testCountries[Math.floor(Math.random() * testCountries.length)];
      const randomRiskLevel = testRiskLevels[Math.floor(Math.random() * testRiskLevels.length)];
      const randomStatus = testStatuses[Math.floor(Math.random() * testStatuses.length)];

      // 生成随机IP地址
      const generateRandomIP = () => {
        return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
      };

      // 生成当前时间戳
      const currentTime = new Date()
        .toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        })
        .replace(/\//g, "-");

      // 模拟 xdrAlarmAndShijian 消息格式
      const testAlarmData = {
        msgType: "xdrAlarmAndShijian",
        msgContent: [
          {
            id: `test_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
            alarmType: num1++,
            lastTimestamp: currentTime,
            hazardRating: randomRiskLevel,
            attackRes: randomStatus,
            attackCountry: randomCountry,
            attackSip: generateRandomIP(),
            hostIp: generateRandomIP(),
            description: `检测到来自${randomCountry}的${randomAttackType}`,
            warnType: "网络安全",
            warnTypeDesc: `${randomAttackType}威胁检测`,
            riskTag: randomRiskLevel,
            name: `攻击事件_${Date.now()}`,
            hazardLevel: randomRiskLevel,
          },
        ],
      };

      console.log("🚀 添加测试攻击数据:", testAlarmData);

      // 调用现有的处理方法
      this.handleXdrAlarmData(testAlarmData);
    },

    // 关闭 WebSocket 连接
    closeWss() {
      if (this.ws) {
        this.ws.close(1000, "主动关闭连接");
        this.ws = null;
        this.wsConnected = false;
      }
    },

    // 发送消息到 WebSocket
    sendWssMessage(message) {
      if (this.ws && this.wsConnected) {
        try {
          this.ws.send(JSON.stringify(message));
          console.log("🚀 WebSocket 发送消息:", message);
        } catch (error) {
          console.error("🚀 WebSocket 发送消息失败:", error);
        }
      } else {
        console.warn("🚀 WebSocket 未连接，无法发送消息");
      }
    },

    // 处理 WebSocket 消息的主函数
    handleWebSocketMessage(data) {
      try {
        const msgType = data.msgType;
        console.log("🚀 处理消息类型:", msgType);

        switch (msgType) {
          case "colasoft":
            this.handleColasoftData(data);
            break;
          case "activity":
            this.handleActivityData(data);
            break;
          case "warning":
            this.handleWarningData(data);
            break;
          case "xdrAlarmAndShijian":
            this.handleXdrAlarmData(data);
            break;
          case "xdrNetworkLog":
            this.handleXdrNetworkLogData(data);
            break;
          default:
            console.log("🚀 未知消息类型:", msgType);
            // 默认处理：更新原有数据结构
            if (data && data.data) {
              this.data = data.data;
            }
            break;
        }
      } catch (error) {
        console.error("🚀 处理 WebSocket 消息失败:", error);
      }
    },

    // 处理 colasoft 类型数据
    handleColasoftData(data) {
      try {
        if (data && data.msgContent && Array.isArray(data.msgContent)) {
          const msgContent = data.msgContent;

          // 创建包含时间和数据的对象数组
          const dataWithTime = msgContent.map((item, index) => {
            return {
              time: item.time || `${index + 1}`,
              totalByte: item.total_byte || 0,
              totalMegabyte: item.total_megabyte || 0,
              originalIndex: index,
              rawItem: item,
            };
          });

          // 按时间排序（如果时间格式为 HH:MM）
          const sortedData = dataWithTime.sort((a, b) => {
            // 如果时间格式为 HH:MM，进行时间排序
            if (a.time.includes(":") && b.time.includes(":")) {
              try {
                const timeA = a.time.split(":").map((num) => parseInt(num.trim()));
                const timeB = b.time.split(":").map((num) => parseInt(num.trim()));

                // 转换为分钟数进行比较（便于处理跨天情况）
                const minutesA = timeA[0] * 60 + (timeA[1] || 0);
                const minutesB = timeB[0] * 60 + (timeB[1] || 0);

                return minutesA - minutesB;
              } catch (error) {
                // 解析失败时按原始索引排序
                return a.originalIndex - b.originalIndex;
              }
            }

            // 如果不是时间格式，按原始索引排序
            return a.originalIndex - b.originalIndex;
          });

          // 限制显示的数据点数量（可选，避免图表过于拥挤）
          const maxDataPoints = 200; // 最多显示20个数据点
          const finalData =
            sortedData.length > maxDataPoints
              ? sortedData.slice(-maxDataPoints) // 取最新的数据点
              : sortedData;

          // 提取最终的数据
          const timeData = finalData.map((item) => item.time);
          const totalByteData = finalData.map((item) => item.totalByte);
          const totalMegabyteData = finalData.map((item) => item.totalMegabyte);

          // 更新 colasoft 数据
          this.colasoftData = {
            xAxisData: timeData,
            seriesData: totalMegabyteData.some((val) => val > 0) ? totalMegabyteData : totalByteData,
            rawData: finalData.map((item) => item.rawItem), // 保存处理后的原始数据
            sortedData: finalData, // 保存完整的处理数据
            totalCount: sortedData.length, // 保存总数据量
          };
        }
      } catch (error) {
        console.error("🚀 处理 colasoft 数据失败:", error);
      }
    },

    // 处理 activity 类型数据
    handleActivityData(data) {
      try {
        console.log("🚀 处理 activity 数据:", data);
        // 更新原有数据结构
        if (data && data.data) {
          this.data = data.data;
        }
      } catch (error) {
        console.error("🚀 处理 activity 数据失败:", error);
      }
    },

    // 处理 warning 类型数据
    handleWarningData(data) {
      try {
        console.log("🚀 处理 warning 数据:", data);
        // 可以根据需要处理警告数据
        if (data && data.data) {
          this.data = { ...this.data, ...data.data };
        }
      } catch (error) {
        console.error("🚀 处理 warning 数据失败:", error);
      }
    },

    // 处理 xdrNetworkLog 类型数据
    handleXdrNetworkLogData(data) {
      try {
        console.log("🚀 处理 xdrNetworkLog 数据:", data);

        if (data && data.msgContent && Array.isArray(data.msgContent)) {
          console.log("🚀 xdrNetworkLog msgContent 数组长度:", data.msgContent.length);

          // 这里只做基本的数据验证和日志记录
          // 实际的飞线生成由 SpherePoints 组件处理
          data.msgContent.forEach((item, index) => {
            if (item.startArray && item.endArray) {
              console.log(`🚀 xdrNetworkLog 第${index + 1}项:`, {
                startArray: item.startArray,
                endArray: item.endArray,
                // 可以记录其他相关信息
                cardInfo: item.cardInfo,
                msgType: item.msgType,
              });
            } else {
              console.warn(`🚀 xdrNetworkLog 第${index + 1}项缺少坐标数据:`, item);
            }
          });
        } else {
          console.warn("🚀 xdrNetworkLog msgContent 不是数组或为空");
        }
      } catch (error) {
        console.error("🚀 处理 xdrNetworkLog 数据失败:", error);
      }
    },

    // 处理 xdrAlarmAndShijian 类型数据
    handleXdrAlarmData(data) {
      try {
        console.log("🚀 处理 xdrAlarmAndShijian 数据:", data);

        if (data && data.msgContent) {
          const msgContent = data.msgContent;

          // 检查 msgContent 是否为数组
          if (Array.isArray(msgContent)) {
            console.log("🚀 msgContent 是数组，包含", msgContent.length, "个告警数据");

            // 处理数组中的每个告警数据
            msgContent.forEach((alarmItem, index) => {
              try {
                // 转换数据格式以适配 Label2 组件
                // 根据新的字段映射要求：
                // alarmType → 标题, lastTimestamp → 攻击时间, hazardRating → 风险等级
                // attackRes → 攻击状态, attackCountry → 攻击来源, attackSip → 攻击主机, hostIp → 被攻击主机
                const formattedAlarm = {
                  id: alarmItem.id || `${Date.now()}_${index}`,
                  // 使用 lastTimestamp 作为攻击时间
                  time: alarmItem.lastTimestamp,
                  // 直接使用 hazardRating 作为风险等级
                  riskLevel: alarmItem.hazardRating || "未知",
                  // 直接使用 attackRes 作为攻击状态
                  status: alarmItem.attackRes || "未知",
                  // 使用 attackCountry 作为攻击来源
                  source: alarmItem.attackCountry || "未知",
                  // 使用 attackSip 作为攻击主机
                  attackHost: alarmItem.attackSip || "未知",
                  // 使用 hostIp 作为被攻击主机
                  targetHost: alarmItem.hostIp || "未知",
                  // 保存原始时间戳用于排序
                  originalTimestamp: alarmItem.lastTimestamp || Date.now(),
                  // 保存原始数据
                  alarmType: alarmItem.alarmType,
                  attackRes: alarmItem.attackRes,
                  hazardRating: alarmItem.hazardRating,
                  description: alarmItem.description,
                  warnType: alarmItem.warnType,
                  warnTypeDesc: alarmItem.warnTypeDesc,
                  riskTag: alarmItem.riskTag,
                  name: alarmItem.name, // 添加 name 字段
                  hazardLevel: alarmItem.hazardLevel, // 添加 hazardLevel 字段
                };

                // 添加到攻击告警数据列表
                this.attackAlarmData.push(formattedAlarm);

                console.log(`🚀 处理第 ${index + 1} 个告警数据:`, formattedAlarm);
              } catch (itemError) {
                console.error(`🚀 处理第 ${index + 1} 个告警数据失败:`, itemError, alarmItem);
              }
            });
          } else {
            // 兼容原有的单个对象格式
            console.log("🚀 msgContent 是单个对象，使用原有处理逻辑");

            const formattedAlarm = {
              id: msgContent.id || Date.now(),
              // 使用 lastTimestamp 作为攻击时间
              time: msgContent.lastTimestamp,
              // 直接使用 hazardRating 作为风险等级
              riskLevel: msgContent.hazardRating || "未知",
              // 直接使用 attackRes 作为攻击状态
              status: msgContent.attackRes || "未知",
              // 使用 attackCountry 作为攻击来源
              source: msgContent.attackCountry || "未知",
              // 使用 attackSip 作为攻击主机
              attackHost: msgContent.attackSip || "未知",
              // 使用 hostIp 作为被攻击主机
              targetHost: msgContent.hostIp || "未知",
              // 保存原始时间戳用于排序
              originalTimestamp: msgContent.lastTimestamp || Date.now(),
              // 保存原始数据
              alarmType: msgContent.alarmType,
              attackRes: msgContent.attackRes,
              hazardRating: msgContent.hazardRating,
              description: msgContent.description,
              warnType: msgContent.warnType,
              warnTypeDesc: msgContent.warnTypeDesc,
              riskTag: msgContent.riskTag,
              name: msgContent.name, // 添加 name 字段
              hazardLevel: msgContent.hazardLevel, // 添加 hazardLevel 字段
            };

            // 添加到攻击告警数据列表
            this.attackAlarmData.push(formattedAlarm);
          }

          // 按时间排序，最新的时间在前面
          this.attackAlarmData.sort((a, b) => {
            const timeA = this.parseTimestampForSort(a.originalTimestamp);
            const timeB = this.parseTimestampForSort(b.originalTimestamp);
            return timeB - timeA; // 降序排列，最新的在前面
          });

          // 限制数据数量，避免内存占用过多
          const maxAlarms = 50;
          if (this.attackAlarmData.length > maxAlarms) {
            this.attackAlarmData = this.attackAlarmData.slice(0, maxAlarms);
          }

          console.log("🚀 xdrAlarmAndShijian 数据处理完成，当前告警数据数量:", this.attackAlarmData.length);
        }
      } catch (error) {
        console.error("🚀 处理 xdrAlarmAndShijian 数据失败:", error);
      }
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return "未知时间";

      try {
        // 如果是字符串格式的时间，直接返回
        if (typeof timestamp === "string" && timestamp.includes("-")) {
          return timestamp;
        }

        // 如果是时间戳，转换为日期格式
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
          return timestamp.toString();
        }

        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        });
      } catch (error) {
        console.warn("🚀 时间格式化失败:", timestamp, error);
        return timestamp.toString();
      }
    },

    // 映射危险等级
    mapHazardLevel(level) {
      const levelMap = {
        10: "低级",
        20: "低级",
        30: "中级",
        40: "中级",
        50: "中级",
        60: "高级",
        70: "高级",
        80: "高级",
        90: "严重",
        100: "严重",
      };

      return levelMap[level] || "未知";
    },

    // 映射处理状态
    mapDealStatus(status) {
      const statusMap = {
        0: "待处理",
        1: "处理中",
        2: "已处理",
        3: "已忽略",
        "": "待确认",
      };

      return statusMap[status] || "未知";
    },

    // 解析时间戳用于排序
    parseTimestampForSort(timestamp) {
      try {
        if (!timestamp) return 0;

        // 如果是数字类型的时间戳，直接返回
        if (typeof timestamp === "number") {
          return timestamp;
        }

        // 如果是字符串，尝试解析为日期
        if (typeof timestamp === "string") {
          const date = new Date(timestamp);
          if (!isNaN(date.getTime())) {
            return date.getTime();
          }
        }

        // 如果解析失败，返回当前时间
        return Date.now();
      } catch (error) {
        console.warn("🚀 时间戳解析失败:", timestamp, error);
        return Date.now();
      }
    },
  },
});
