import * as THREE from "three";
import { EARTH_RADIUS } from "../../../../constants";
import { GUI } from "lil-gui";
import { vertexShader, fragmentShader } from "./shaders/shaderLoader.js";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";

class HoneycombGrid {
  constructor(scene, { isActive = true, renderer, opacity = 0.2, color = "#00ffff" } = {}) {
    this.scene = scene;
    this.isActive = isActive;
    this.renderer = renderer;
    this.mesh = null;
    this.geometry = null;
    this.material = null;

    // Honeycomb parameters
    this.opacity = 0.01;
    this.color = color;
    this.hexSize = 0.8; // Size of each hexagon
    this.gridDensity = 32; // Number of divisions for the sphere
    this.lineWidth = 0.02; // Width of the honeycomb lines

    // Grid size and density parameters for GUI control
    this.hexagonSize = 0.12; // Individual hexagon size (0.2 - 2.0)
    this.latitudeDivisions = 192; // Latitude divisions (12 - 48)
    this.longitudeDivisions = 384; // Longitude divisions (24 - 96)

    // Animation properties
    this.time = 0;
    this.animationSpeed = 0.001;
    this.pulseIntensity = 0.1;

    // Sweep effect properties
    this.sweepEnabled = true;
    this.sweepSpeed = 1.0; // 增加默认速度
    this.sweepWidth = 0.34; // 减少宽度让效果更集中
    this.sweepIntensity = 0.33; // 增加强度让效果更明显
    this.sweepFalloff = 1; // 扫光边缘过渡强度 (0=柔和, 1=锐利)
    this.sweepRotation = 66; // 扫光旋转角度 (0-360度)
    this.sweepDirection = new THREE.Vector3(0, -1, 0); // 自上而下
    this.intensityDecay = 4; // 强度衰减速度 (0=无衰减, 5=快速衰减)

    // Shader uniforms
    this.uniforms = null;

    // GUI properties
    this.gui = null;
    this.folder = null;

    // Color options for GUI
    this.colorOptions = {
      绿色: "#00ff88",
      蓝色: "#0088ff",
      红色: "#ff0088",
      紫色: "#8800ff",
      橙色: "#ff8800",
      青色: "#00ffff",
    };

    this.init();
  }

  async init() {
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();
    // this.initGUI();
  }

  createGeometry() {
    // Create custom honeycomb geometry
    this.geometry = this.createHoneycombGeometry();
  }

  createHoneycombGeometry() {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const indices = [];

    const radius = EARTH_RADIUS * 1.001; // 增加半径，确保在所有地球层之上

    // 蜂窝网格参数 - 使用正确的六边形密铺数学
    const hexRadius = this.hexagonSize; // 六边形外接圆半径

    // 计算球面上的网格密度
    const latitudeDivisions = this.latitudeDivisions; // 纬度分割数
    const longitudeDivisions = this.longitudeDivisions; // 经度分割数

    // 生成球面六边形网格 - 修复对称性问题
    for (let lat = 0; lat <= latitudeDivisions; lat++) {
      // 修复: 使用 <= 并调整 phi 计算以确保对称覆盖
      const phi = (lat / latitudeDivisions) * Math.PI; // 0 to π (包含南极)
      const sinPhi = Math.sin(phi);
      const cosPhi = Math.cos(phi);

      // 在极点附近跳过以避免过度密集
      if (sinPhi < 0.05) {
        // 在极点附近只放置少量hexagon或跳过
        if (lat === 0) {
          // 北极点 - 只放置一个中心hexagon
          this.createHexagon(vertices, indices, 0, radius, 0, hexRadius);
        } else if (lat === latitudeDivisions) {
          // 南极点 - 只放置一个中心hexagon
          this.createHexagon(vertices, indices, 0, -radius, 0, hexRadius);
        }
        continue;
      }

      // 根据纬度调整经度分割数，避免极点处过密
      const lonDivisions = Math.max(3, Math.floor(longitudeDivisions * sinPhi));

      for (let lon = 0; lon < lonDivisions; lon++) {
        // 六边形密铺的偏移模式 - 改进偏移计算
        const offset = (lat % 2) * 0.5;
        const theta = ((lon + offset) / lonDivisions) * Math.PI * 2;

        // 球面坐标转换为笛卡尔坐标
        const x = radius * sinPhi * Math.cos(theta);
        const y = radius * cosPhi;
        const z = radius * sinPhi * Math.sin(theta);

        // 创建统一大小的六边形
        this.createHexagon(vertices, indices, x, y, z, hexRadius);
      }
    }

    geometry.setAttribute("position", new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setIndex(indices);

    return geometry;
  }

  createHexagon(vertices, indices, centerX, centerY, centerZ, size) {
    const center = new THREE.Vector3(centerX, centerY, centerZ);
    const normal = center.clone().normalize();

    // Create local coordinate system on sphere surface
    const up = Math.abs(normal.y) < 0.9 ? new THREE.Vector3(0, 1, 0) : new THREE.Vector3(1, 0, 0);
    const right = new THREE.Vector3().crossVectors(up, normal).normalize();
    const forward = new THREE.Vector3().crossVectors(normal, right).normalize();

    const startIndex = vertices.length / 3;
    const radius = EARTH_RADIUS * 1.008;

    // Create hexagon vertices - maintain consistent angular size on sphere
    for (let i = 0; i < 6; i++) {
      const angle = (i / 6) * Math.PI * 2;

      // Use angular displacement for consistent size on sphere
      const angularSize = size / radius; // Convert linear size to angular size
      const localX = Math.cos(angle) * angularSize;
      const localY = Math.sin(angle) * angularSize;

      // Calculate vertex position on sphere surface
      const vertex = center
        .clone()
        .add(right.clone().multiplyScalar(localX * radius))
        .add(forward.clone().multiplyScalar(localY * radius));

      // Normalize to sphere surface
      vertex.normalize().multiplyScalar(radius);

      vertices.push(vertex.x, vertex.y, vertex.z);
    }

    // Create hexagon edges
    for (let i = 0; i < 6; i++) {
      const next = (i + 1) % 6;
      indices.push(startIndex + i, startIndex + next);
    }
  }

  createMaterial() {
    // Create uniforms for the shader
    this.uniforms = {
      uTime: { value: 0.0 },
      uSweepDirection: { value: this.sweepDirection.clone() },
      uSweepSpeed: { value: this.sweepSpeed },
      uSweepWidth: { value: this.sweepWidth },
      uSweepIntensity: { value: this.sweepIntensity },
      uSweepFalloff: { value: this.sweepFalloff },
      uSweepRotation: { value: this.sweepRotation },
      uBaseOpacity: { value: this.opacity },
      uColor: { value: new THREE.Color(this.color) },
      uIntensityDecay: { value: this.intensityDecay },
    };

    this.material = new THREE.ShaderMaterial({
      vertexShader: vertexShader,
      fragmentShader: fragmentShader,
      uniforms: this.uniforms,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
      depthTest: true,
      fog: false,
      side: THREE.DoubleSide,
    });
  }

  createMesh() {
    // Use LineSegments for the wireframe grid
    this.mesh = new THREE.LineSegments(this.geometry, this.material);
    this.mesh.position.set(0, 0, 0);
    const scale = 0.998;
    this.mesh.scale.set(scale, scale, scale);
    this.mesh.renderOrder = 100; // 设置更高的渲染顺序，确保在所有地球层之后渲染
  }

  addToScene() {
    if (this.scene && this.mesh && this.isActive) {
      this.scene.add(this.mesh);
    }
  }

  // Animation update method
  update() {
    if (this.mesh && this.material && this.uniforms) {
      // Use performance.now() for more accurate timing
      const currentTime = performance.now() * 0.001; // Convert to seconds
      this.time = currentTime;

      // Update shader uniforms
      this.uniforms.uTime.value = this.time;

      // Update sweep parameters if they've changed
      this.uniforms.uSweepSpeed.value = this.sweepSpeed;
      this.uniforms.uSweepWidth.value = this.sweepWidth;
      this.uniforms.uSweepIntensity.value = this.sweepEnabled ? this.sweepIntensity : 0.0;
      this.uniforms.uSweepFalloff.value = this.sweepFalloff;
      this.uniforms.uSweepRotation.value = this.sweepRotation;
      this.uniforms.uBaseOpacity.value = this.opacity;
      this.uniforms.uSweepDirection.value.copy(this.sweepDirection);
      this.uniforms.uIntensityDecay.value = this.intensityDecay;

      // Optional: Rotate the grid slowly - speed affected by animationSpeed
      this.mesh.rotation.y += this.animationSpeed * 0.5;
    }
  }

  // Method to toggle visibility
  setVisible(visible) {
    this.isActive = visible;
    if (this.mesh) {
      this.mesh.visible = visible;
    }
  }

  // Method to change color
  setColor(color) {
    this.color = color;
    if (this.uniforms) {
      this.uniforms.uColor.value.set(color);
    }
  }

  // Method to change opacity
  setOpacity(opacity) {
    this.opacity = opacity;
    if (this.uniforms) {
      this.uniforms.uBaseOpacity.value = opacity;
    }
  }

  // Method to change animation speed
  setAnimationSpeed(speed) {
    this.animationSpeed = speed;
  }

  // Method to toggle sweep effect
  setSweepEnabled(enabled) {
    this.sweepEnabled = enabled;
  }

  // Method to change sweep speed
  setSweepSpeed(speed) {
    this.sweepSpeed = speed;
  }

  // Method to change sweep width
  setSweepWidth(width) {
    this.sweepWidth = width;
  }

  // Method to change sweep intensity
  setSweepIntensity(intensity) {
    this.sweepIntensity = intensity;
  }

  // Method to change sweep falloff
  setSweepFalloff(falloff) {
    this.sweepFalloff = falloff;
  }

  // Method to change sweep rotation
  setSweepRotation(rotation) {
    this.sweepRotation = rotation;
  }

  // Method to change intensity decay
  setIntensityDecay(decay) {
    this.intensityDecay = decay;
  }

  // Method to update hexagon size
  setHexagonSize(size) {
    this.hexagonSize = size;
    this.hexSize = size; // Keep backward compatibility
    this.regenerateGeometry();
  }

  // Method to update latitude divisions
  setLatitudeDivisions(divisions) {
    this.latitudeDivisions = Math.round(divisions);
    this.regenerateGeometry();
  }

  // Method to update longitude divisions
  setLongitudeDivisions(divisions) {
    this.longitudeDivisions = Math.round(divisions);
    this.regenerateGeometry();
  }

  // Method to regenerate geometry with new parameters
  regenerateGeometry() {
    if (!this.mesh) return;

    // Dispose old geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Create new geometry
    this.createGeometry();

    // Update mesh geometry
    this.mesh.geometry = this.geometry;
  }

  // Initialize GUI controls
  initGUI() {
    // Create GUI if it doesn't exist
    if (!window.honeycombGridGUI) {
      this.gui = new GUI({
        title: "🔸 蜂窝网格控制",
        width: 280,
      });
      window.honeycombGridGUI = this.gui;
    } else {
      this.gui = window.honeycombGridGUI;
    }

    // Create honeycomb grid folder
    this.folder = this.gui.addFolder("蜂窝网格设置");
    this.folder.open();

    this.setupGUIControls();
  }

  // Setup GUI controls
  setupGUIControls() {
    // Visibility toggle
    this.folder
      .add(this, "isActive")
      .name("显示/隐藏")
      .onChange((value) => {
        this.setVisible(value);
      });

    // Color picker
    this.folder
      .addColor(this, "color")
      .name("颜色")
      .onChange((value) => {
        this.setColor(value);
      });

    // Opacity slider
    this.folder
      .add(this, "opacity", 0.01, 1.0, 0.01)
      .name("透明度")
      .onChange((value) => {
        this.setOpacity(value);
      });

    // Animation speed slider (normalized for GUI)
    const animationSpeedControl = {
      speed: 1.0,
    };

    this.folder
      .add(animationSpeedControl, "speed", 0, 2, 0.1)
      .name("动画速度")
      .onChange((value) => {
        this.animationSpeed = 0.001 * value;
      });

    // Hexagon size slider
    this.folder
      .add(this, "hexagonSize", 0.1, 2.0, 0.01)
      .name("六边形大小")
      .onChange((value) => {
        this.setHexagonSize(value);
      });

    // Latitude divisions slider
    this.folder
      .add(this, "latitudeDivisions", 12, 400, 1)
      .name("纬度密度")
      .onChange((value) => {
        this.setLatitudeDivisions(value);
      });

    // Longitude divisions slider
    this.folder
      .add(this, "longitudeDivisions", 24, 400, 1)
      .name("经度密度")
      .onChange((value) => {
        this.setLongitudeDivisions(value);
      });

    // Sweep effect controls
    this.folder
      .add(this, "sweepEnabled")
      .name("启用扫光效果")
      .onChange((value) => {
        this.setSweepEnabled(value);
      });

    this.folder
      .add(this, "sweepSpeed", 0.01, 2.0, 0.01)
      .name("扫光速度")
      .onChange((value) => {
        this.setSweepSpeed(value);
      });

    this.folder
      .add(this, "sweepWidth", 0.2, 2.0, 0.01)
      .name("扫光宽度")
      .onChange((value) => {
        this.setSweepWidth(value);
      });

    this.folder
      .add(this, "sweepIntensity", 0.0, 1.0, 0.01)
      .name("扫光强度")
      .onChange((value) => {
        this.setSweepIntensity(value);
      });

    this.folder
      .add(this, "sweepFalloff", 0.0, 1.0, 0.01)
      .name("边缘过渡")
      .onChange((value) => {
        this.setSweepFalloff(value);
      });

    this.folder
      .add(this, "sweepRotation", 0, 360, 1)
      .name("扫光旋转")
      .onChange((value) => {
        this.setSweepRotation(value);
      });

    this.folder
      .add(this, "intensityDecay", 0.0, 5.0, 0.1)
      .name("强度衰减速度")
      .onChange((value) => {
        this.setIntensityDecay(value);
      });
  }

  // Method to get current settings
  getSettings() {
    return {
      isVisible: this.mesh ? this.mesh.visible : false,
      color: this.color,
      opacity: this.opacity,
      animationSpeed: this.animationSpeed,
      hexagonSize: this.hexagonSize,
      latitudeDivisions: this.latitudeDivisions,
      longitudeDivisions: this.longitudeDivisions,
    };
  }

  destroy() {
    // Remove GUI folder
    if (this.folder && this.gui) {
      this.gui.removeFolder(this.folder);
    }

    // Clean up GUI if no other folders exist
    if (this.gui && this.gui.folders.length === 0) {
      this.gui.destroy();
      window.honeycombGridGUI = null;
    }

    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.gui = null;
    this.folder = null;
  }
}

// Export both named and default exports for flexibility
export { HoneycombGrid };
export default HoneycombGrid;
